# Docker Compose configuration for production environment
version: '3.8'

services:
  frontend:
    image: ${FRONTEND_IMAGE:-ghcr.io/your-username/ukshati.2.0/frontend:latest}
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=db
      - DB_USER=company
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=company_db
      - CHOKIDAR_USEPOLLING=false
      - WATCHPACK_POLLING=false
    depends_on:
      db:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  db:
    image: ${DB_IMAGE:-ghcr.io/your-username/ukshati.2.0/database:latest}
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: company_db
      MYSQL_USER: company
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql-production-data:/var/lib/mysql
      - ./backups:/backups
      - ./logs:/var/log/mysql
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    healthcheck:
      test: ["CMD-SHELL", "mysqladmin ping -u company --password=$$MYSQL_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
    networks:
      - app-network
    restart: unless-stopped

volumes:
  mysql-production-data:
    driver: local

networks:
  app-network:
    driver: bridge
