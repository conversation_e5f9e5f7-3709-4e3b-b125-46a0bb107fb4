name: 🔄 CI/CD Pipeline

on:
  push:
    branches: [ github-actions ]
  pull_request:
    branches: [ github-actions ]

env:
  NODE_VERSION: '20'

jobs:
  # 🧪 Test and Build
  test-build:
    name: 🧪 Test & Build
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: company_db_test
          MYSQL_USER: company
          MYSQL_PASSWORD: Ukshati@123
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: 📦 Install Dependencies
        working-directory: ./frontend
        run: npm ci

      - name: 🔍 Lint Code
        working-directory: ./frontend
        run: |
          echo "Running ESLint..."
          npx next lint

      - name: 🏗️ Build Application
        working-directory: ./frontend
        run: npm run build

      - name: 🔐 Security Audit
        working-directory: ./frontend
        run: npm audit --audit-level=high

  # 🐳 Docker Build and Deploy
  deploy:
    name: 🐳 Build & Deploy
    runs-on: ubuntu-latest
    needs: test-build
    if: github.ref == 'refs/heads/github-actions'

    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🐳 Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 🏗️ Build and Push Images
        run: |
          # Build and tag images
          docker build -t ghcr.io/${{ github.repository }}/frontend:${{ github.sha }} .
          docker build -f Dockerfile.mysql -t ghcr.io/${{ github.repository }}/database:${{ github.sha }} .

          # Push images
          docker push ghcr.io/${{ github.repository }}/frontend:${{ github.sha }}
          docker push ghcr.io/${{ github.repository }}/database:${{ github.sha }}

      - name: 🚀 Deploy to Server (TEST MODE)
        if: github.ref == 'refs/heads/github-actions'
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          script: |
            echo "🧪 TEST MODE: Would deploy to /opt/ukshati"
            echo "🧪 TEST MODE: Would run docker-compose pull"
            echo "🧪 TEST MODE: Would run docker-compose up -d"
            echo "✅ TEST MODE: Deployment simulation completed!"

      - name: 📢 Notify Team
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Deployment successful!"
          else
            echo "❌ Deployment failed!"
          fi
