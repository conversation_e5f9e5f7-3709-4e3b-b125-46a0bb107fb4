name: 🔄 Continuous Integration

on:
  push:
    branches: [ main, purchase-order, code-structure, restructure, github-actions ]
  pull_request:
    branches: [ main, purchase-order ]

env:
  NODE_VERSION: '20'
  MYSQL_VERSION: '8.0'

jobs:
  # 🧪 Testing & Quality Checks
  test-and-quality:
    name: 🧪 Tests & Quality Checks
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: company_db_test
          MYSQL_USER: company
          MYSQL_PASSWORD: Ukshati@123
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping -h localhost"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: 📦 Install Dependencies
        working-directory: ./frontend
        run: |
          npm ci
          npm audit --audit-level=high

      - name: 🗄️ Setup Test Database
        run: |
          mysql -h 127.0.0.1 -u root -proot -e "CREATE DATABASE IF NOT EXISTS company_db_test;"
          mysql -h 127.0.0.1 -u root -proot company_db_test < db/company_db.sql

      - name: 🔍 ESLint Check
        working-directory: ./frontend
        run: npm run lint

      - name: 🏗️ Build Check
        working-directory: ./frontend
        env:
          NODE_ENV: production
          DB_HOST: 127.0.0.1
          DB_USER: company
          DB_PASSWORD: Ukshati@123
          DB_NAME: company_db_test
        run: npm run build

      - name: 🧪 Run Tests (if available)
        working-directory: ./frontend
        env:
          NODE_ENV: test
          DB_HOST: 127.0.0.1
          DB_USER: company
          DB_PASSWORD: Ukshati@123
          DB_NAME: company_db_test
        run: |
          # Add test command when tests are implemented
          echo "Tests will be added here when test suite is ready"
          # npm test

  # 🔒 Security Scanning
  security-scan:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: 🔍 Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 📊 Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: 🔐 Dependency Security Audit
        working-directory: ./frontend
        run: |
          npm audit --audit-level=moderate
          npx audit-ci --moderate

  # 🐳 Docker Build Test
  docker-build-test:
    name: 🐳 Docker Build Test
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🏗️ Build Frontend Image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: false
          tags: ukshati-frontend:test
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 🗄️ Build Database Image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.mysql
          push: false
          tags: ukshati-db:test
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 🧪 Test Docker Compose
        run: |
          echo "Testing docker-compose configuration..."
          docker-compose config
          echo "✅ Docker Compose configuration is valid"

  # 📊 Code Quality Analysis
  code-quality:
    name: 📊 Code Quality Analysis
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: 📦 Install Dependencies
        working-directory: ./frontend
        run: npm ci

      - name: 🔍 SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          args: >
            -Dsonar.projectKey=ukshati-2.0
            -Dsonar.organization=your-org
            -Dsonar.sources=frontend/
            -Dsonar.exclusions=**/node_modules/**,**/*.test.js
            -Dsonar.javascript.lcov.reportPaths=frontend/coverage/lcov.info

  # ✅ Status Check
  ci-status:
    name: ✅ CI Status Check
    runs-on: ubuntu-latest
    needs: [test-and-quality, security-scan, docker-build-test, code-quality]
    if: always()
    
    steps:
      - name: 📊 Check CI Results
        run: |
          echo "🔍 Checking CI pipeline results..."
          
          if [[ "${{ needs.test-and-quality.result }}" == "success" && 
                "${{ needs.security-scan.result }}" == "success" && 
                "${{ needs.docker-build-test.result }}" == "success" ]]; then
            echo "✅ All CI checks passed successfully!"
            exit 0
          else
            echo "❌ Some CI checks failed:"
            echo "  - Tests & Quality: ${{ needs.test-and-quality.result }}"
            echo "  - Security Scan: ${{ needs.security-scan.result }}"
            echo "  - Docker Build: ${{ needs.docker-build-test.result }}"
            echo "  - Code Quality: ${{ needs.code-quality.result }}"
            exit 1
          fi
