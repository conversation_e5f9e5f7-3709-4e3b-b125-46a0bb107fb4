name: 🚀 Continuous Deployment

on:
  push:
    branches: [ main, purchase-order ]
  workflow_run:
    workflows: ["🔄 Continuous Integration"]
    types:
      - completed
    branches: [ main, purchase-order ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME_FRONTEND: ${{ github.repository }}/frontend
  IMAGE_NAME_DB: ${{ github.repository }}/database
  NODE_VERSION: '20'

jobs:
  # 🏗️ Build and Push Docker Images
  build-and-push:
    name: 🏗️ Build & Push Images
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success' || github.event_name == 'push'
    
    outputs:
      frontend-image: ${{ steps.meta-frontend.outputs.tags }}
      db-image: ${{ steps.meta-db.outputs.tags }}
      frontend-digest: ${{ steps.build-frontend.outputs.digest }}
      db-digest: ${{ steps.build-db.outputs.digest }}
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 🏷️ Extract Frontend Metadata
        id: meta-frontend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_FRONTEND }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: 🏷️ Extract Database Metadata
        id: meta-db
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_DB }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: 🏗️ Build and Push Frontend Image
        id: build-frontend
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta-frontend.outputs.tags }}
          labels: ${{ steps.meta-frontend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: 🏗️ Build and Push Database Image
        id: build-db
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.mysql
          push: true
          tags: ${{ steps.meta-db.outputs.tags }}
          labels: ${{ steps.meta-db.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  # 🗄️ Database Migration
  database-migration:
    name: 🗄️ Database Migration
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/purchase-order'
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: 🗄️ Run Database Migrations
        env:
          DB_HOST: ${{ secrets.DB_HOST_STAGING }}
          DB_USER: ${{ secrets.DB_USER }}
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
          DB_NAME: ${{ secrets.DB_NAME_STAGING }}
        run: |
          echo "🔄 Running database migrations..."
          # Install MySQL client
          sudo apt-get update
          sudo apt-get install -y mysql-client
          
          # Check database connection
          mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD -e "SELECT 1;" $DB_NAME
          
          # Run migrations (if migration files exist)
          if [ -f "db/migrations.sql" ]; then
            echo "📝 Applying database migrations..."
            mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME < db/migrations.sql
          else
            echo "📝 Applying main database schema..."
            mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME < db/company_db.sql
          fi
          
          echo "✅ Database migration completed successfully!"

  # 🚀 Deploy to Staging
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-and-push, database-migration]
    if: github.ref == 'refs/heads/purchase-order'
    environment: staging
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Staging Server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          port: ${{ secrets.STAGING_PORT }}
          script: |
            echo "🚀 Starting deployment to staging..."
            
            # Navigate to project directory
            cd /opt/ukshati-staging
            
            # Pull latest images
            docker login ${{ env.REGISTRY }} -u ${{ github.actor }} -p ${{ secrets.GITHUB_TOKEN }}
            docker pull ${{ needs.build-and-push.outputs.frontend-image }}
            docker pull ${{ needs.build-and-push.outputs.db-image }}
            
            # Update docker-compose with new images
            export FRONTEND_IMAGE="${{ needs.build-and-push.outputs.frontend-image }}"
            export DB_IMAGE="${{ needs.build-and-push.outputs.db-image }}"
            
            # Stop existing containers
            docker-compose down
            
            # Start new containers
            docker-compose up -d
            
            # Health check
            sleep 30
            curl -f http://localhost:3000/api/health || exit 1
            
            echo "✅ Staging deployment completed successfully!"

      - name: 🧪 Run Staging Tests
        run: |
          echo "🧪 Running staging environment tests..."
          # Add staging-specific tests here
          curl -f ${{ secrets.STAGING_URL }}/api/health
          echo "✅ Staging tests passed!"

  # 🌟 Deploy to Production
  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-and-push, database-migration]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: 🌟 Deploy to Production Server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USER }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          port: ${{ secrets.PRODUCTION_PORT }}
          script: |
            echo "🌟 Starting deployment to production..."
            
            # Navigate to project directory
            cd /opt/ukshati-production
            
            # Create backup
            docker-compose exec db mysqldump -u company -pUkshati@123 company_db > backup_$(date +%Y%m%d_%H%M%S).sql
            
            # Pull latest images
            docker login ${{ env.REGISTRY }} -u ${{ github.actor }} -p ${{ secrets.GITHUB_TOKEN }}
            docker pull ${{ needs.build-and-push.outputs.frontend-image }}
            docker pull ${{ needs.build-and-push.outputs.db-image }}
            
            # Update docker-compose with new images
            export FRONTEND_IMAGE="${{ needs.build-and-push.outputs.frontend-image }}"
            export DB_IMAGE="${{ needs.build-and-push.outputs.db-image }}"
            
            # Rolling update
            docker-compose up -d --no-deps frontend
            
            # Health check
            sleep 30
            curl -f http://localhost:3000/api/health || exit 1
            
            echo "✅ Production deployment completed successfully!"

      - name: 🧪 Run Production Smoke Tests
        run: |
          echo "🧪 Running production smoke tests..."
          curl -f ${{ secrets.PRODUCTION_URL }}/api/health
          echo "✅ Production smoke tests passed!"

  # 📢 Notifications
  notify:
    name: 📢 Send Notifications
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
      - name: 📢 Slack Notification
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}

      - name: 📧 Email Notification
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: smtp.gmail.com
          server_port: 587
          username: ${{ secrets.EMAIL_USERNAME }}
          password: ${{ secrets.EMAIL_PASSWORD }}
          subject: "🚀 Ukshati 2.0 Deployment Status: ${{ job.status }}"
          to: ${{ secrets.NOTIFICATION_EMAIL }}
          from: "Ukshati CI/CD <${{ secrets.EMAIL_USERNAME }}>"
          body: |
            Deployment Status: ${{ job.status }}
            Branch: ${{ github.ref }}
            Commit: ${{ github.sha }}
            Author: ${{ github.actor }}
            
            Staging: ${{ needs.deploy-staging.result }}
            Production: ${{ needs.deploy-production.result }}
            
            View details: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
