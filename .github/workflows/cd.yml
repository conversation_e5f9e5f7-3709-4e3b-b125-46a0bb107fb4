name: 🚀 Auto Deploy

on:
  workflow_run:
    workflows: ["🔄 CI/CD Pipeline"]
    types: [completed]
    branches: [github-actions]

jobs:
  deploy:
    name: 🚀 Deploy Application
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success'

    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          script: |
            cd /opt/ukshati

            # Pull latest code
            git pull origin ${{ github.ref_name }}

            # Rebuild and restart containers
            docker-compose down
            docker-compose build --no-cache
            docker-compose up -d

            # Clean up old images
            docker system prune -f

            echo "✅ Deployment completed!"

      - name: 📢 Notify Success
        if: success()
        run: echo "🎉 Successfully deployed to server!"

      - name: 📢 Notify Failure
        if: failure()
        run: echo "❌ Deployment failed!"
