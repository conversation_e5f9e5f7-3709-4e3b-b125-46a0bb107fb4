name: 🚀 Auto Deploy

on:
  workflow_run:
    workflows: ["🔄 CI/CD Pipeline"]
    types: [completed]
    branches: [github-actions]

jobs:
  deploy:
    name: 🧪 Test Deploy (github-actions branch)
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success'

    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          script: |
            echo "🧪 TEST MODE: CI/CD Pipeline Testing"
            echo "🧪 Branch: ${{ github.ref_name }}"
            echo "🧪 Would pull from: origin/${{ github.ref_name }}"
            echo "🧪 Would rebuild Docker containers"
            echo "🧪 Would restart application"
            echo "✅ TEST MODE: All deployment steps simulated successfully!"

      - name: 📢 Notify Success
        if: success()
        run: echo "🎉 Successfully deployed to server!"

      - name: 📢 Notify Failure
        if: failure()
        run: echo "❌ Deployment failed!"
