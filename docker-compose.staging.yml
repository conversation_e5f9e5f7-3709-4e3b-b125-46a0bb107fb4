# Docker Compose configuration for staging environment
version: '3.8'

services:
  frontend:
    image: ${FRONTEND_IMAGE:-ghcr.io/your-username/ukshati.2.0/frontend:latest}
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=staging
      - DB_HOST=db
      - DB_USER=company
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=company_db_staging
      - CHOKIDAR_USEPOLLING=false
      - WATCHPACK_POLLING=false
    depends_on:
      db:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  db:
    image: ${DB_IMAGE:-ghcr.io/your-username/ukshati.2.0/database:latest}
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: company_db_staging
      MYSQL_USER: company
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql-staging-data:/var/lib/mysql
      - ./backups:/backups
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "mysqladmin ping -u company --password=$$MYSQL_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

volumes:
  mysql-staging-data:
    driver: local

networks:
  app-network:
    driver: bridge
