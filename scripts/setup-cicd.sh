#!/bin/bash

# 🚀 Ukshati 2.0 CI/CD Setup Script
# This script helps set up the CI/CD pipeline prerequisites

set -e

echo "🚀 Setting up Ukshati 2.0 CI/CD Pipeline"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running in git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "This script must be run from within a git repository"
    exit 1
fi

print_info "Checking prerequisites..."

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    print_warning "GitHub CLI (gh) is not installed. Please install it for easier secret management."
    print_info "Install from: https://cli.github.com/"
else
    print_status "GitHub CLI is installed"
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_warning "Docker is not installed. Please install Docker for local testing."
    print_info "Install from: https://docs.docker.com/get-docker/"
else
    print_status "Docker is installed"
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_warning "Docker Compose is not available. Please install Docker Compose."
else
    print_status "Docker Compose is available"
fi

echo ""
print_info "Setting up project structure..."

# Create necessary directories
mkdir -p .github/workflows
mkdir -p scripts
mkdir -p logs
mkdir -p backups

print_status "Created necessary directories"

# Make scripts executable
chmod +x scripts/setup-cicd.sh

print_status "Made scripts executable"

echo ""
print_info "Checking environment files..."

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating template..."
    cat > .env << EOF
# Database Configuration
DB_HOST=localhost
DB_USER=company
DB_PASSWORD=Ukshati@123
DB_NAME=company_db

# Application Configuration
NODE_ENV=development
PORT=3000

# Add other environment variables as needed
EOF
    print_status "Created .env template"
else
    print_status ".env file exists"
fi

echo ""
print_info "Setting up GitHub repository secrets..."

if command -v gh &> /dev/null; then
    echo "Do you want to set up GitHub secrets now? (y/n)"
    read -r setup_secrets
    
    if [ "$setup_secrets" = "y" ] || [ "$setup_secrets" = "Y" ]; then
        echo ""
        print_info "Setting up GitHub secrets..."
        
        # Database secrets
        echo "Enter staging database host:"
        read -r staging_db_host
        gh secret set DB_HOST_STAGING --body "$staging_db_host"
        
        echo "Enter production database host:"
        read -r production_db_host
        gh secret set DB_HOST_PRODUCTION --body "$production_db_host"
        
        echo "Enter database password:"
        read -s db_password
        gh secret set DB_PASSWORD --body "$db_password"
        
        echo "Enter MySQL root password:"
        read -s mysql_root_password
        gh secret set MYSQL_ROOT_PASSWORD --body "$mysql_root_password"
        
        # Server secrets
        echo "Enter staging server host:"
        read -r staging_host
        gh secret set STAGING_HOST --body "$staging_host"
        
        echo "Enter staging server user:"
        read -r staging_user
        gh secret set STAGING_USER --body "$staging_user"
        
        echo "Enter staging URL:"
        read -r staging_url
        gh secret set STAGING_URL --body "$staging_url"
        
        echo "Enter production server host:"
        read -r production_host
        gh secret set PRODUCTION_HOST --body "$production_host"
        
        echo "Enter production server user:"
        read -r production_user
        gh secret set PRODUCTION_USER --body "$production_user"
        
        echo "Enter production URL:"
        read -r production_url
        gh secret set PRODUCTION_URL --body "$production_url"
        
        print_status "GitHub secrets configured"
        print_warning "Don't forget to add SSH keys and notification secrets manually!"
    fi
else
    print_warning "GitHub CLI not available. Please set up secrets manually in GitHub repository settings."
fi

echo ""
print_info "Validating workflow files..."

# Check if workflow files exist
if [ -f ".github/workflows/ci.yml" ]; then
    print_status "CI workflow file exists"
else
    print_error "CI workflow file missing"
fi

if [ -f ".github/workflows/cd.yml" ]; then
    print_status "CD workflow file exists"
else
    print_error "CD workflow file missing"
fi

echo ""
print_info "Testing Docker configuration..."

# Test docker-compose configuration
if docker-compose config > /dev/null 2>&1; then
    print_status "Docker Compose configuration is valid"
else
    print_warning "Docker Compose configuration has issues"
fi

echo ""
print_info "Setting up git hooks (optional)..."

# Create pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Pre-commit hook for Ukshati 2.0

echo "🔍 Running pre-commit checks..."

# Check if we're in frontend directory for linting
if [ -d "frontend" ]; then
    cd frontend
    
    # Run linting
    if npm run lint; then
        echo "✅ Linting passed"
    else
        echo "❌ Linting failed"
        exit 1
    fi
    
    cd ..
fi

echo "✅ Pre-commit checks passed"
EOF

chmod +x .git/hooks/pre-commit
print_status "Created pre-commit hook"

echo ""
print_status "CI/CD setup completed!"
echo ""
print_info "Next steps:"
echo "1. Review and customize the workflow files in .github/workflows/"
echo "2. Set up your staging and production servers"
echo "3. Configure remaining GitHub secrets (SSH keys, notifications)"
echo "4. Test the pipeline by creating a pull request"
echo "5. Monitor the Actions tab in your GitHub repository"
echo ""
print_info "For detailed instructions, see CI_CD_SETUP.md"
