/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: true,
    // Enable standalone output for Docker optimization
    output: 'standalone',
    images: {
      domains: [
        'www.mishainfotech.com',
        'img.freepik.com',
        'miro.medium.com',
        'www.itarian.com',
        'aavenir.com',
        'everpro.id',
        'png.pngtree.com'
      ],
      unoptimized: true // For Docker builds
    },
    // Environment variables
    env: {
      CUSTOM_KEY: process.env.CUSTOM_KEY,
    },
    // Experimental features for better performance
    experimental: {
      // Enable SWC minification
      swcMinify: true,
    },
    // Webpack configuration
    webpack: (config, { isServer }) => {
      // Optimize bundle size
      if (!isServer) {
        config.resolve.fallback = {
          ...config.resolve.fallback,
          fs: false,
        };
      }
      return config;
    },
};

export default nextConfig;