/**
 * UI Components Index
 * Centralized exports for all UI components
 */

// Import components
import Button from './Button/Button';
import Card from './Card/Card';
import Modal from './Modal/Modal';
import LoadingSpinner from './LoadingSpinner/LoadingSpinner';
import Table from './Table/Table';
import MetricCard from './MetricCard/MetricCard';
import Tabs, { TabPanel } from './Tabs/Tabs';
import { Form, FormGroup, Label, Input, Select, Textarea, SearchInput } from './Form/Form';

// Named exports
export {
  Button,
  Card,
  Modal,
  LoadingSpinner,
  Table,
  MetricCard,
  Tabs,
  TabPanel,
  Form,
  FormGroup,
  Label,
  Input,
  Select,
  Textarea,
  SearchInput
};

// Default export
export default {
  Button,
  Card,
  Modal,
  LoadingSpinner,
  Table,
  MetricCard,
  Tabs,
  TabPanel,
  Form,
  FormGroup,
  Label,
  Input,
  Select,
  Textarea,
  SearchInput,
};
