// Health check endpoint for CI/CD monitoring
import mysql from 'mysql2/promise';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '2.0.0',
    environment: process.env.NODE_ENV || 'development',
    uptime: process.uptime(),
    checks: {
      database: 'unknown',
      memory: 'unknown',
      disk: 'unknown'
    }
  };

  try {
    // Database health check
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'company',
      password: process.env.DB_PASSWORD || 'Ukshati@123',
      database: process.env.DB_NAME || 'company_db',
      connectTimeout: 5000
    });

    await connection.execute('SELECT 1');
    await connection.end();
    healthCheck.checks.database = 'healthy';

    // Memory usage check
    const memUsage = process.memoryUsage();
    const memUsageMB = {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024)
    };

    healthCheck.checks.memory = {
      status: memUsageMB.heapUsed < 500 ? 'healthy' : 'warning',
      usage: memUsageMB
    };

    // Overall status determination
    const allChecksHealthy = Object.values(healthCheck.checks).every(
      check => check === 'healthy' || (typeof check === 'object' && check.status === 'healthy')
    );

    if (!allChecksHealthy) {
      healthCheck.status = 'degraded';
      return res.status(503).json(healthCheck);
    }

    return res.status(200).json(healthCheck);

  } catch (error) {
    healthCheck.status = 'unhealthy';
    healthCheck.checks.database = 'unhealthy';
    healthCheck.error = error.message;

    return res.status(503).json(healthCheck);
  }
}
