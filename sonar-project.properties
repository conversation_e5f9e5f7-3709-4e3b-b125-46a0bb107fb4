# SonarCloud configuration for code quality analysis
sonar.projectKey=ukshati-2.0
sonar.organization=your-organization-key

# Project information
sonar.projectName=Ukshati 2.0
sonar.projectVersion=2.0

# Source code configuration
sonar.sources=frontend/
sonar.sourceEncoding=UTF-8

# Exclusions
sonar.exclusions=**/node_modules/**,**/*.test.js,**/*.spec.js,**/coverage/**,**/build/**,**/dist/**

# Test configuration
sonar.tests=frontend/
sonar.test.inclusions=**/*.test.js,**/*.spec.js
sonar.test.exclusions=**/node_modules/**

# Coverage configuration
sonar.javascript.lcov.reportPaths=frontend/coverage/lcov.info
sonar.coverage.exclusions=**/*.test.js,**/*.spec.js,**/coverage/**

# Language-specific settings
sonar.javascript.node.maxspace=4096
