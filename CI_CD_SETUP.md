# 🚀 Ukshati 2.0 CI/CD Implementation Guide

## 📋 Overview

This document provides a comprehensive guide for the GitHub Actions CI/CD pipeline implementation for Ukshati 2.0. The pipeline automates testing, building, security scanning, and deployment processes.

## 🏗️ Architecture

### Pipeline Structure
- **CI Pipeline** (`ci.yml`): Testing, Quality Checks, Security Scanning
- **CD Pipeline** (`cd.yml`): Building, Deployment, Notifications

### Workflow Triggers
- **CI**: Triggered on push/PR to `main`, `purchase-order`, `code-structure`, `restructure`, `github-actions`
- **CD**: Triggered on successful CI completion for `main` and `purchase-order` branches

## 🔧 Prerequisites

### 1. Repository Secrets Setup
Configure the following secrets in GitHub repository settings:

#### Database Configuration
```
DB_HOST_STAGING=your-staging-db-host
DB_HOST_PRODUCTION=your-production-db-host
DB_USER=company
DB_PASSWORD=Ukshati@123
DB_NAME_STAGING=company_db_staging
DB_NAME_PRODUCTION=company_db
MYSQL_ROOT_PASSWORD=your-root-password
```

#### Server Configuration
```
STAGING_HOST=your-staging-server-ip
STAGING_USER=deploy
STAGING_SSH_KEY=your-staging-ssh-private-key
STAGING_PORT=22
STAGING_URL=https://staging.ukshati.com

PRODUCTION_HOST=your-production-server-ip
PRODUCTION_USER=deploy
PRODUCTION_SSH_KEY=your-production-ssh-private-key
PRODUCTION_PORT=22
PRODUCTION_URL=https://ukshati.com
```

#### Notification Configuration
```
SLACK_WEBHOOK=your-slack-webhook-url
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
NOTIFICATION_EMAIL=<EMAIL>
```

#### Code Quality
```
SONAR_TOKEN=your-sonarcloud-token
```

### 2. Server Setup

#### Staging Server Setup
```bash
# Create deployment directory
sudo mkdir -p /opt/ukshati-staging
sudo chown deploy:deploy /opt/ukshati-staging

# Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker deploy

# Copy docker-compose.staging.yml to server
scp docker-compose.staging.yml deploy@staging-server:/opt/ukshati-staging/docker-compose.yml
```

#### Production Server Setup
```bash
# Create deployment directory
sudo mkdir -p /opt/ukshati-production
sudo chown deploy:deploy /opt/ukshati-production

# Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker deploy

# Copy docker-compose.production.yml to server
scp docker-compose.production.yml deploy@production-server:/opt/ukshati-production/docker-compose.yml
```

## 🔄 CI Pipeline Details

### 1. Test & Quality Checks Job
- **Node.js Setup**: Uses Node.js 20 with npm caching
- **Database Setup**: Spins up MySQL 8.0 test database
- **Dependency Installation**: Runs `npm ci` with security audit
- **Linting**: Executes ESLint checks
- **Build Verification**: Tests production build
- **Test Execution**: Placeholder for future test implementation

### 2. Security Scanning Job
- **Trivy Scanner**: Scans for vulnerabilities in filesystem
- **Dependency Audit**: Checks npm packages for security issues
- **SARIF Upload**: Uploads scan results to GitHub Security tab

### 3. Docker Build Test Job
- **Multi-platform Builds**: Tests AMD64 and ARM64 architectures
- **Cache Optimization**: Uses GitHub Actions cache
- **Configuration Validation**: Validates docker-compose files

### 4. Code Quality Analysis Job
- **SonarCloud Integration**: Analyzes code quality and coverage
- **Technical Debt Tracking**: Monitors code maintainability
- **Security Hotspots**: Identifies potential security issues

## 🚀 CD Pipeline Details

### 1. Build & Push Job
- **Multi-stage Docker Builds**: Optimized production images
- **Container Registry**: Pushes to GitHub Container Registry
- **Image Tagging**: Branch-based and SHA-based tags
- **Multi-platform Support**: AMD64 and ARM64 architectures

### 2. Database Migration Job
- **Schema Updates**: Applies database migrations
- **Connection Testing**: Validates database connectivity
- **Rollback Support**: Maintains backup capabilities

### 3. Staging Deployment Job
- **Environment**: Deploys to staging on `purchase-order` branch
- **Health Checks**: Validates deployment success
- **Rollback Capability**: Automatic rollback on failure

### 4. Production Deployment Job
- **Environment**: Deploys to production on `main` branch
- **Backup Creation**: Creates database backup before deployment
- **Rolling Updates**: Zero-downtime deployment strategy
- **Smoke Tests**: Post-deployment validation

### 5. Notification Job
- **Slack Integration**: Sends deployment status to team channel
- **Email Notifications**: Detailed deployment reports
- **Status Tracking**: Success/failure notifications

## 🔒 Security Features

### 1. Vulnerability Scanning
- **Container Scanning**: Trivy scans Docker images
- **Dependency Scanning**: npm audit for package vulnerabilities
- **SARIF Integration**: Results visible in GitHub Security tab

### 2. Secret Management
- **GitHub Secrets**: Secure storage of sensitive data
- **Environment Isolation**: Separate secrets for staging/production
- **Access Control**: Limited secret access per environment

### 3. Image Security
- **Multi-stage Builds**: Minimal attack surface
- **Non-root User**: Containers run as non-privileged user
- **Distroless Base**: Reduced vulnerability footprint

## 📊 Monitoring & Observability

### 1. Health Checks
- **Application Health**: `/api/health` endpoint
- **Database Connectivity**: MySQL connection validation
- **Resource Monitoring**: Memory and CPU usage tracking

### 2. Logging
- **Structured Logs**: JSON format for better parsing
- **Log Rotation**: Prevents disk space issues
- **Centralized Logging**: Ready for log aggregation

### 3. Metrics
- **Deployment Metrics**: Success/failure rates
- **Performance Metrics**: Build and deployment times
- **Quality Metrics**: Code coverage and technical debt

## 🛠️ Maintenance & Best Practices

### 1. Regular Updates
- **Dependabot**: Automated dependency updates
- **Security Patches**: Regular base image updates
- **Action Updates**: Keep GitHub Actions up to date

### 2. Branch Strategy
- **Main Branch**: Production deployments
- **Purchase-order**: Staging deployments
- **Feature Branches**: Development and testing

### 3. Rollback Procedures
- **Database Backups**: Automated before deployments
- **Image Rollback**: Previous image tags available
- **Configuration Rollback**: Git-based configuration management

## 🚨 Troubleshooting

### Common Issues
1. **Build Failures**: Check dependency versions and Node.js compatibility
2. **Database Connection**: Verify credentials and network connectivity
3. **Deployment Failures**: Check server resources and Docker daemon
4. **Test Failures**: Review test logs and database setup

### Debug Commands
```bash
# Check container logs
docker-compose logs -f frontend

# Verify database connection
docker-compose exec db mysql -u company -p

# Check container health
docker-compose ps
```

## 📞 Support

For issues with the CI/CD pipeline:
1. Check GitHub Actions logs
2. Review this documentation
3. Contact the DevOps team
4. Create an issue in the repository

## 🔄 Next Steps

1. **Test Implementation**: Add comprehensive test suite
2. **Performance Monitoring**: Implement APM tools
3. **Blue-Green Deployment**: Advanced deployment strategies
4. **Infrastructure as Code**: Terraform for server provisioning
